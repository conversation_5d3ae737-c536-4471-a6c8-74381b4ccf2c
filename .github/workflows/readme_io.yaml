name: Sync OpenAPI to ReadMe

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Sync OpenAPI definition to ReadMe
        uses: readmeio/rdme@v10
        with:
          rdme: |
            openapi ./services/data_service/openapi_stable.json \
              --key=${{ secrets.MY_LLIF_README_KEY }} \
              --id=64d608b3473525005f6cec73
